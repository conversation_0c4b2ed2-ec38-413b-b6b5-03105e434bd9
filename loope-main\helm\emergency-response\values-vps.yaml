# VPS-specific values for Emergency Response Helm Chart
# Override default values for VPS deployment

# Global settings
global:
  imageRegistry: "localhost:32000"
  storageClass: "microk8s-hostpath"

# Application configuration
app:
  name: emergency-response-app
  replicaCount: 2
  image:
    repository: localhost:32000/emergency-response-app
    tag: v1.0.0
    pullPolicy: Always
  
  service:
    type: ClusterIP
    port: 3000
    targetPort: 3000
  
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  
  env:
    FLASK_ENV: "production"
    DATABASE_URL: "sqlite:///app.db"
    REDIS_URL: "redis://redis-service:6379"
    PORT: "3000"
    SECRET_KEY: "vps-production-secret-key-2025"
    MAIL_SERVER: "smtp.gmail.com"
    MAIL_PORT: "587"
    MAIL_USE_TLS: "true"
    MAIL_USERNAME: "<EMAIL>"
    MAIL_PASSWORD: "your-app-password"

# Monitoring configuration
monitoring:
  name: monitoring-dashboard
  replicaCount: 1
  image:
    repository: localhost:32000/monitoring-dashboard
    tag: v1.0.0
    pullPolicy: Always
  
  service:
    type: ClusterIP
    port: 9999
    targetPort: 9999
  
  resources:
    limits:
      cpu: 300m
      memory: 256Mi
    requests:
      cpu: 150m
      memory: 128Mi

# Redis configuration
redis:
  enabled: true
  image:
    repository: redis
    tag: 7-alpine
  
  service:
    type: ClusterIP
    port: 6379
  
  persistence:
    enabled: true
    size: 1Gi
    storageClass: microk8s-hostpath

# Nginx configuration
nginx:
  name: nginx-proxy
  replicaCount: 2
  image:
    repository: localhost:32000/nginx-proxy
    tag: v1.0.0
    pullPolicy: Always
  
  service:
    type: LoadBalancer
    port: 8888
    targetPort: 80
    loadBalancerIP: "***********"
  
  resources:
    limits:
      cpu: 200m
      memory: 128Mi
    requests:
      cpu: 100m
      memory: 64Mi

# Ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
  hosts:
    - host: ***********
      paths:
        - path: /
          pathType: Prefix
          service:
            name: nginx-service
            port: 8888

# Autoscaling configuration
autoscaling:
  enabled: true
  app:
    minReplicas: 2
    maxReplicas: 5
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  monitoring:
    minReplicas: 1
    maxReplicas: 3
    targetCPUUtilizationPercentage: 75
    targetMemoryUtilizationPercentage: 85
  
  nginx:
    minReplicas: 2
    maxReplicas: 4
    targetCPUUtilizationPercentage: 60
    targetMemoryUtilizationPercentage: 70

# Persistence configuration
persistence:
  enabled: true
  storageClass: microk8s-hostpath
  app:
    data:
      size: 2Gi
    logs:
      size: 1Gi
  monitoring:
    data:
      size: 1Gi

# Security configuration
security:
  podSecurityContext:
    runAsNonRoot: true
    runAsUser: 1000
    fsGroup: 1000
  
  securityContext:
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: false
    capabilities:
      drop:
        - ALL

# Health checks
healthChecks:
  enabled: true
  app:
    livenessProbe:
      httpGet:
        path: /api/v1/health
        port: 3000
      initialDelaySeconds: 30
      periodSeconds: 10
    readinessProbe:
      httpGet:
        path: /api/v1/health
        port: 3000
      initialDelaySeconds: 5
      periodSeconds: 5

# Monitoring and observability
monitoring:
  prometheus:
    enabled: true
    scrapeInterval: 30s
  
  grafana:
    enabled: false  # Using our custom monitoring dashboard
  
  metrics:
    enabled: true
    port: 9090

# Network policies
networkPolicy:
  enabled: false  # Disabled for simplicity in VPS deployment

# Resource quotas
resourceQuota:
  enabled: false  # Disabled for VPS deployment
