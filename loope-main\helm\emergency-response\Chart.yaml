apiVersion: v2
name: emergency-response
description: A Helm chart for Emergency Response App - Cameroon Emergency Services Platform
type: application
version: 1.0.0
appVersion: "1.0.0"
home: https://github.com/emergency-response/emergency-app
sources:
  - https://github.com/emergency-response/emergency-app
maintainers:
  - name: Emergency Response Team
    email: <EMAIL>
keywords:
  - emergency
  - response
  - cameroon
  - healthcare
  - fire-department
  - flask
  - python
annotations:
  category: Healthcare
  licenses: MIT
dependencies:
  - name: redis
    version: "17.3.7"
    repository: "https://charts.bitnami.com/bitnami"
    condition: redis.enabled
  - name: prometheus
    version: "15.5.3"
    repository: "https://prometheus-community.github.io/helm-charts"
    condition: monitoring.prometheus.enabled
  - name: grafana
    version: "6.50.7"
    repository: "https://grafana.github.io/helm-charts"
    condition: monitoring.grafana.enabled
