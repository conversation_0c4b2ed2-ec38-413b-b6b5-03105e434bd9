all:
  hosts:
    emergency-app-server:
      ansible_host: *************
      ansible_user: root
      ansible_ssh_common_args: '-o StrictHostKeyChecking=no'
      ansible_python_interpreter: /usr/bin/python3

  children:
    web_servers:
      hosts:
        emergency-app-server:
      vars:
        app_name: emergency-response-app
        app_port: 3000
        app_user: emergency
        app_directory: /opt/emergency-app
        server_name: srv838312.hstgr.cloud

    monitoring_servers:
      hosts:
        emergency-app-server:
      vars:
        prometheus_port: 9090
        grafana_port: 3001
        alertmanager_port: 9093
        node_exporter_port: 9100
