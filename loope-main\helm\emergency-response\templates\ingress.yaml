{{- if .Values.ingress.enabled -}}
{{- $fullName := include "emergency-response.fullname" . -}}
{{- $svcPort := .Values.nginx.service.port -}}
{{- if and .Values.ingress.className (not (hasKey .Values.ingress.annotations "kubernetes.io/ingress.class")) }}
  {{- $_ := set .Values.ingress.annotations "kubernetes.io/ingress.class" .Values.ingress.className}}
{{- end }}
{{- if semverCompare ">=1.19-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1
{{- else if semverCompare ">=1.14-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1beta1
{{- else -}}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ $fullName }}-ingress
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "emergency-response.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.ingress.className (semverCompare ">=1.18-0" .Capabilities.KubeVersion.GitVersion) }}
  ingressClassName: {{ .Values.ingress.className }}
  {{- end }}
  {{- if .Values.ingress.tls.enabled }}
  tls:
    - hosts:
        {{- range .Values.ingress.hosts }}
        - {{ .host | quote }}
        {{- end }}
      secretName: {{ .Values.ingress.tls.secretName }}
  {{- end }}
  rules:
    {{- range .Values.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- if and .pathType (semverCompare ">=1.18-0" $.Capabilities.KubeVersion.GitVersion) }}
            pathType: {{ .pathType }}
            {{- end }}
            backend:
              {{- if semverCompare ">=1.19-0" $.Capabilities.KubeVersion.GitVersion }}
              service:
                name: {{ $fullName }}-{{ .service }}
                port:
                  number: {{ if eq .service "nginx" }}{{ $.Values.nginx.service.port }}{{ else if eq .service "app" }}{{ $.Values.app.service.port }}{{ else if eq .service "monitoring" }}{{ $.Values.monitoring.dashboard.service.port }}{{ end }}
              {{- else }}
              serviceName: {{ $fullName }}-{{ .service }}
              servicePort: {{ if eq .service "nginx" }}{{ $.Values.nginx.service.port }}{{ else if eq .service "app" }}{{ $.Values.app.service.port }}{{ else if eq .service "monitoring" }}{{ $.Values.monitoring.dashboard.service.port }}{{ end }}
              {{- end }}
          {{- end }}
    {{- end }}
{{- end }}
