apiVersion: v1
kind: Namespace
metadata:
  name: emergency-response
  labels:
    name: emergency-response
    app: emergency-response-app
    version: v1.0.0
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: emergency-response-quota
  namespace: emergency-response
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    pods: "20"
    services: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: emergency-response-limits
  namespace: emergency-response
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
