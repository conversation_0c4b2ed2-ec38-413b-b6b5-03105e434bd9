{% extends "base.html" %}

{% block title %}Community Chat - Emergency Response App{% endblock %}

{% block navbar %}
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="{{ url_for('landing') }}">
            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
        </a>

        <div class="navbar-nav ms-auto">
            <span class="navbar-text me-3">
                <i class="fas fa-user me-1"></i>{{ current_user.full_name }}
            </span>
            <a class="nav-link" href="{{ url_for('landing') }}">
                <i class="fas fa-home me-1"></i>Home
            </a>
        </div>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="container-fluid mt-3">
    <div class="row">
        <div class="col-12">
            <!-- Chat Header -->
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">
                            <i class="fas fa-comments me-2"></i>Community Emergency Chat
                        </h5>
                        <small>Real-time communication for emergency response community</small>
                    </div>
                    <div>
                        <span class="badge bg-success me-2" id="onlineCount">{{ online_users or 1 }} online</span>
                        <button class="btn btn-sm btn-light" onclick="refreshChat()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>

                <!-- Chat Messages Area -->
                <div class="card-body p-0">
                    <div id="chatMessages" class="chat-messages" style="height: 400px; overflow-y: auto; padding: 15px;">
                        <!-- Messages will be loaded here -->
                        {% for message in messages %}
                        <div class="message-item mb-3" data-message-id="{{ message.id }}">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="avatar bg-{% if message.user_type == 'fire_department' %}danger{% else %}primary{% endif %} text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-{% if message.user_type == 'fire_department' %}fire-extinguisher{% else %}user{% endif %}"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="message-header d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong class="text-{% if message.user_type == 'fire_department' %}danger{% else %}primary{% endif %}">
                                                {{ message.full_name }}
                                                {% if message.user_type == 'fire_department' %}
                                                <span class="badge bg-danger ms-1">Fire Dept</span>
                                                {% endif %}
                                            </strong>
                                            <small class="text-muted ms-2">{{ message.created_at }}</small>
                                        </div>
                                        {% if message.user_id == current_user.id %}
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="editMessage({{ message.id }})">
                                                    <i class="fas fa-edit me-2"></i>Edit
                                                </a></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteMessage({{ message.id }})">
                                                    <i class="fas fa-trash me-2"></i>Delete
                                                </a></li>
                                            </ul>
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="message-content mt-1">
                                        <p class="mb-1">{{ message.content }}</p>
                                        {% if message.message_type == 'emergency' %}
                                        <span class="badge bg-danger">🚨 Emergency</span>
                                        {% elif message.message_type == 'alert' %}
                                        <span class="badge bg-warning">⚠️ Alert</span>
                                        {% elif message.message_type == 'info' %}
                                        <span class="badge bg-info">ℹ️ Info</span>
                                        {% endif %}
                                    </div>
                                    <div class="message-actions mt-2">
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="likeMessage({{ message.id }})">
                                            <i class="fas fa-thumbs-up"></i> <span id="likes-{{ message.id }}">{{ message.likes or 0 }}</span>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="replyToMessage({{ message.id }})">
                                            <i class="fas fa-reply"></i> Reply
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Message Input Area -->
                <div class="card-footer">
                    <form id="messageForm" class="d-flex">
                        <div class="flex-grow-1 me-2">
                            <div class="input-group">
                                <select class="form-select" id="messageType" style="max-width: 120px;">
                                    <option value="general">💬 General</option>
                                    <option value="info">ℹ️ Info</option>
                                    <option value="alert">⚠️ Alert</option>
                                    <option value="emergency">🚨 Emergency</option>
                                </select>
                                <input type="text" class="form-control" id="messageInput" placeholder="Type your message..." required maxlength="500">
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Messages are visible to all community members. Be respectful and keep discussions emergency-related.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_scripts %}
<script>
// Global variables
let lastMessageId = 0;
let isScrolledToBottom = true;

// Initialize chat when page loads
document.addEventListener('DOMContentLoaded', function() {
    scrollToBottom();
    loadMessages();

    // Auto-scroll detection
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.addEventListener('scroll', function() {
        isScrolledToBottom = (chatMessages.scrollTop + chatMessages.clientHeight >= chatMessages.scrollHeight - 10);
    });
});

// Handle message form submission
document.getElementById('messageForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const messageInput = document.getElementById('messageInput');
    const messageType = document.getElementById('messageType').value;
    const content = messageInput.value.trim();

    if (!content) return;

    // Send message to server
    fetch('/send-message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            content: content,
            message_type: messageType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            messageInput.value = '';
            loadMessages(); // Refresh messages
        } else {
            alert('Failed to send message: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error sending message:', error);
        alert('Failed to send message. Please try again.');
    });
});

function loadMessages() {
    fetch('/get-messages')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateMessages(data.messages);
            }
        })
        .catch(error => {
            console.error('Error loading messages:', error);
        });
}

function updateMessages(messages) {
    const chatMessages = document.getElementById('chatMessages');
    const shouldScroll = isScrolledToBottom;

    // Clear existing messages
    chatMessages.innerHTML = '';

    // Add messages
    messages.forEach(message => {
        const messageElement = createMessageElement(message);
        chatMessages.appendChild(messageElement);
    });

    // Scroll to bottom if user was at bottom
    if (shouldScroll) {
        scrollToBottom();
    }
}

function createMessageElement(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message-item mb-3';
    messageDiv.setAttribute('data-message-id', message.id);

    const isCurrentUser = message.user_id == {{ current_user.id }};
    const isFireDept = message.user_type === 'fire_department';

    messageDiv.innerHTML = `
        <div class="d-flex">
            <div class="flex-shrink-0">
                <div class="avatar bg-${isFireDept ? 'danger' : 'primary'} text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                    <i class="fas fa-${isFireDept ? 'fire-extinguisher' : 'user'}"></i>
                </div>
            </div>
            <div class="flex-grow-1 ms-3">
                <div class="message-header d-flex justify-content-between align-items-center">
                    <div>
                        <strong class="text-${isFireDept ? 'danger' : 'primary'}">
                            ${message.full_name}
                            ${isFireDept ? '<span class="badge bg-danger ms-1">Fire Dept</span>' : ''}
                        </strong>
                        <small class="text-muted ms-2">${formatTime(message.created_at)}</small>
                    </div>
                    ${isCurrentUser ? `
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteMessage(${message.id})">
                                <i class="fas fa-trash me-2"></i>Delete
                            </a></li>
                        </ul>
                    </div>
                    ` : ''}
                </div>
                <div class="message-content mt-1">
                    <p class="mb-1">${escapeHtml(message.content)}</p>
                    ${getMessageTypeBadge(message.message_type)}
                </div>
                <div class="message-actions mt-2">
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="likeMessage(${message.id})">
                        <i class="fas fa-thumbs-up"></i> <span id="likes-${message.id}">${message.likes || 0}</span>
                    </button>
                    <small class="text-muted">${formatTime(message.created_at)}</small>
                </div>
            </div>
        </div>
    `;

    return messageDiv;
}

function getMessageTypeBadge(type) {
    switch(type) {
        case 'emergency':
            return '<span class="badge bg-danger">🚨 Emergency</span>';
        case 'alert':
            return '<span class="badge bg-warning">⚠️ Alert</span>';
        case 'info':
            return '<span class="badge bg-info">ℹ️ Info</span>';
        default:
            return '';
    }
}

function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;

    return date.toLocaleDateString();
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function scrollToBottom() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function refreshChat() {
    loadMessages();
}

function deleteMessage(messageId) {
    if (confirm('Are you sure you want to delete this message?')) {
        fetch('/delete-message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message_id: messageId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadMessages(); // Refresh messages
            } else {
                alert('Failed to delete message: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error deleting message:', error);
            alert('Failed to delete message. Please try again.');
        });
    }
}

function likeMessage(messageId) {
    fetch('/like-message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            message_id: messageId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById(`likes-${messageId}`).textContent = data.likes;
        }
    })
    .catch(error => {
        console.error('Error liking message:', error);
    });
}

// Auto-refresh messages every 10 seconds
setInterval(loadMessages, 10000);

// Handle Enter key in message input
document.getElementById('messageInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        document.getElementById('messageForm').dispatchEvent(new Event('submit'));
    }
});
</script>

<style>
.chat-messages {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.message-item {
    background-color: white;
    border-radius: 0.5rem;
    padding: 10px;
    margin-bottom: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.avatar {
    font-size: 0.9rem;
}

.message-content p {
    word-wrap: break-word;
    white-space: pre-wrap;
}

.message-actions button {
    font-size: 0.8rem;
}

.dropdown-menu {
    font-size: 0.9rem;
}

/* Scrollbar styling */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
{% endblock %}
