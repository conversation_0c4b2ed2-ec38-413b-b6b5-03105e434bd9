version: '3.8'

services:
  # Emergency Response App
  emergency-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: emergency-app
    ports:
      - "3000:5000"
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
      - ./static:/app/static
      - ./templates:/app/templates
    environment:
      - FLASK_ENV=production
      - DATABASE_PATH=/app/data/emergency.db
      - PORT=5000
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - emergency-network

  # Monitoring Dashboard
  monitoring-dashboard:
    build:
      context: .
      dockerfile: Dockerfile.monitoring
    container_name: monitoring-dashboard
    ports:
      - "9999:9999"
    environment:
      - PORT=9999
    volumes:
      - ./coverage.xml:/app/coverage.xml:ro
      - ./htmlcov:/app/htmlcov:ro
      - ./tests:/app/tests:ro
    networks:
      - emergency-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9999/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy
  nginx:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    container_name: emergency-nginx
    ports:
      - "80:80"
    depends_on:
      - emergency-app
      - monitoring-dashboard
    networks:
      - emergency-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: emergency-prometheus-dev
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/alert_rules.yml:/etc/prometheus/alert_rules.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - emergency-network
    restart: unless-stopped

  # Grafana for dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: emergency-grafana-dev
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=emergency123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    networks:
      - emergency-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # Alertmanager for alerts
  alertmanager:
    image: prom/alertmanager:latest
    container_name: emergency-alertmanager-dev
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - emergency-network
    restart: unless-stopped

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: emergency-node-exporter-dev
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - emergency-network
    restart: unless-stopped

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: emergency-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - emergency-network
    restart: unless-stopped

volumes:
  app_data:
    driver: local
  app_logs:
    driver: local
  prometheus_data:
  grafana_data:
  alertmanager_data:
  redis_data:

networks:
  emergency-network:
    driver: bridge
