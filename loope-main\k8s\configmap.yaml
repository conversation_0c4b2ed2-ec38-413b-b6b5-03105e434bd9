apiVersion: v1
kind: ConfigMap
metadata:
  name: emergency-app-config
  namespace: emergency-response
  labels:
    app: emergency-response-app
data:
  FLASK_ENV: "production"
  DATABASE_PATH: "/app/data/emergency.db"
  PORT: "5000"
  LOG_LEVEL: "INFO"
  REDIS_URL: "redis://redis-service:6379"
  PROMETHEUS_METRICS: "true"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: emergency-response
  labels:
    app: emergency-nginx
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        access_log /var/log/nginx/access.log main;

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;

        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;

        upstream emergency_app {
            server emergency-app-service:3000;
            keepalive 32;
        }

        upstream monitoring_dashboard {
            server monitoring-service:9999;
            keepalive 16;
        }

        server {
            listen 80;
            server_name _;

            location / {
                proxy_pass http://emergency_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
                
                proxy_buffering on;
                proxy_buffer_size 4k;
                proxy_buffers 8 4k;
            }

            location /api/ {
                proxy_pass http://emergency_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                proxy_connect_timeout 10s;
                proxy_send_timeout 10s;
                proxy_read_timeout 10s;
            }

            location /monitoring/ {
                proxy_pass http://monitoring_dashboard/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            location /metrics {
                proxy_pass http://emergency_app/metrics;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            }
        }
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: emergency-response
  labels:
    app: prometheus
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
      - "alert_rules.yml"

    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager-service:9093

    scrape_configs:
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']

      - job_name: 'emergency-app'
        static_configs:
          - targets: ['emergency-app-service:5000']
        metrics_path: '/metrics'
        scrape_interval: 30s

      - job_name: 'monitoring-dashboard'
        static_configs:
          - targets: ['monitoring-service:9999']
        metrics_path: '/metrics'
        scrape_interval: 30s

      - job_name: 'nginx'
        static_configs:
          - targets: ['nginx-service:80']
        metrics_path: '/metrics'
        scrape_interval: 30s

      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - emergency-response
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
