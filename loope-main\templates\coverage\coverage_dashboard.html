<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Response App - Test Coverage Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .coverage-good { background-color: #d4edda; color: #155724; }
        .coverage-warning { background-color: #fff3cd; color: #856404; }
        .coverage-poor { background-color: #f8d7da; color: #721c24; }
        .metric-card { transition: transform 0.2s; }
        .metric-card:hover { transform: translateY(-2px); }
        .progress-circle { width: 120px; height: 120px; }
        .file-row:hover { background-color: #f8f9fa; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-dark bg-primary">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                <i class="fas fa-chart-line me-2"></i>
                Emergency Response App - Test Coverage Dashboard
            </span>
            <div class="d-flex">
                <button class="btn btn-outline-light me-2" onclick="runCoverage()">
                    <i class="fas fa-play me-1"></i>Run Coverage
                </button>
                <button class="btn btn-outline-light" onclick="refreshData()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        {% if coverage %}
        <!-- Coverage Overview -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card h-100">
                    <div class="card-body text-center">
                        <div class="progress-circle mx-auto mb-3 d-flex align-items-center justify-content-center rounded-circle {% if coverage.overall_line_coverage >= 85 %}bg-success{% elif coverage.overall_line_coverage >= 70 %}bg-warning{% else %}bg-danger{% endif %}" style="width: 100px; height: 100px;">
                            <h2 class="text-white mb-0">{{ "%.1f"|format(coverage.overall_line_coverage) }}%</h2>
                        </div>
                        <h5 class="card-title">Overall Line Coverage</h5>
                        <p class="card-text">
                            {% if coverage.overall_line_coverage >= 85 %}
                                <span class="badge bg-success">Excellent</span>
                            {% elif coverage.overall_line_coverage >= 70 %}
                                <span class="badge bg-warning">Good</span>
                            {% else %}
                                <span class="badge bg-danger">Needs Improvement</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card metric-card h-100">
                    <div class="card-body text-center">
                        <h2 class="text-primary">{{ coverage.total_files }}</h2>
                        <h5 class="card-title">Total Files</h5>
                        <p class="card-text">
                            <small class="text-muted">{{ coverage.files_above_85 }} files above 85%</small>
                        </p>
                    </div>
                </div>
            </div>
            
            {% if tests %}
            <div class="col-md-3">
                <div class="card metric-card h-100">
                    <div class="card-body text-center">
                        <h2 class="text-success">{{ tests.passed_tests }}</h2>
                        <h5 class="card-title">Tests Passed</h5>
                        <p class="card-text">
                            <small class="text-muted">{{ tests.total_tests }} total tests</small>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card metric-card h-100">
                    <div class="card-body text-center">
                        <h2 class="{% if tests.failed_tests == 0 %}text-success{% else %}text-danger{% endif %}">{{ tests.failed_tests }}</h2>
                        <h5 class="card-title">Tests Failed</h5>
                        <p class="card-text">
                            <small class="text-muted">{{ tests.skipped_tests }} skipped</small>
                        </p>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Coverage Target Progress -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-target me-2"></i>
                            Coverage Target Progress (85% Goal)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3" style="height: 30px;">
                            <div class="progress-bar {% if coverage.overall_line_coverage >= 85 %}bg-success{% elif coverage.overall_line_coverage >= 70 %}bg-warning{% else %}bg-danger{% endif %}" 
                                 role="progressbar" 
                                 style="width: {{ coverage.overall_line_coverage }}%"
                                 aria-valuenow="{{ coverage.overall_line_coverage }}" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                                {{ "%.1f"|format(coverage.overall_line_coverage) }}%
                            </div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>0%</span>
                            <span class="fw-bold">85% Target</span>
                            <span>100%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Coverage Details -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-code me-2"></i>
                            File Coverage Details
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>File</th>
                                        <th>Line Coverage</th>
                                        <th>Lines Covered</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for file in coverage.files %}
                                    <tr class="file-row">
                                        <td>
                                            <i class="fas fa-file-code me-2"></i>
                                            {{ file.filename }}
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar {% if file.line_coverage >= 85 %}bg-success{% elif file.line_coverage >= 70 %}bg-warning{% else %}bg-danger{% endif %}" 
                                                     role="progressbar" 
                                                     style="width: {{ file.line_coverage }}%">
                                                    {{ "%.1f"|format(file.line_coverage) }}%
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ file.covered_lines }} / {{ file.total_lines }}</td>
                                        <td>
                                            {% if file.status == 'good' %}
                                                <span class="badge bg-success">Good</span>
                                            {% elif file.status == 'warning' %}
                                                <span class="badge bg-warning">Warning</span>
                                            {% else %}
                                                <span class="badge bg-danger">Poor</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="/files/{{ file.filename }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i>View Details
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% if tests and tests.test_files %}
        <!-- Test Results -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-vial me-2"></i>
                            Test Results by File
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Test File</th>
                                        <th>Total Tests</th>
                                        <th>Passed</th>
                                        <th>Failed</th>
                                        <th>Skipped</th>
                                        <th>Success Rate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for test_file in tests.test_files %}
                                    <tr>
                                        <td>
                                            <i class="fas fa-vial me-2"></i>
                                            {{ test_file.name }}
                                        </td>
                                        <td>{{ test_file.tests }}</td>
                                        <td><span class="text-success">{{ test_file.passed }}</span></td>
                                        <td><span class="text-danger">{{ test_file.failed }}</span></td>
                                        <td><span class="text-warning">{{ test_file.skipped }}</span></td>
                                        <td>
                                            {% set success_rate = (test_file.passed / test_file.tests * 100) if test_file.tests > 0 else 0 %}
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar {% if success_rate == 100 %}bg-success{% elif success_rate >= 80 %}bg-warning{% else %}bg-danger{% endif %}" 
                                                     role="progressbar" 
                                                     style="width: {{ success_rate }}%">
                                                    {{ "%.0f"|format(success_rate) }}%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        {% else %}
        <!-- No Coverage Data -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h4>No Coverage Data Available</h4>
                        <p class="text-muted">Run the coverage analysis to generate coverage reports.</p>
                        <button class="btn btn-primary" onclick="runCoverage()">
                            <i class="fas fa-play me-2"></i>Run Coverage Analysis
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Footer -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <small class="text-muted">
                            Last updated: {{ coverage.timestamp if coverage else 'Never' }}
                            | Emergency Response App Coverage Dashboard
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function runCoverage() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Running...';
            button.disabled = true;

            fetch('/api/run-coverage', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Coverage analysis completed successfully!');
                        location.reload();
                    } else {
                        alert('Coverage analysis failed: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    alert('Error running coverage: ' + error);
                })
                .finally(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
        }

        function refreshData() {
            location.reload();
        }

        // Auto-refresh every 5 minutes
        setInterval(refreshData, 300000);
    </script>
</body>
</html>
