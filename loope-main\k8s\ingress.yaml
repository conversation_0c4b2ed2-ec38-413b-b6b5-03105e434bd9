apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: emergency-app-ingress
  namespace: emergency-response
  labels:
    app: emergency-response-app
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "30"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  rules:
  - host: emergency-app.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: nginx-service
            port:
              number: 80
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: emergency-app-service
            port:
              number: 5000
      - path: /monitoring
        pathType: Prefix
        backend:
          service:
            name: monitoring-service
            port:
              number: 9999
      - path: /grafana
        pathType: Prefix
        backend:
          service:
            name: grafana-service
            port:
              number: 3000
  - host: monitoring.emergency-app.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: monitoring-service
            port:
              number: 9999
  - host: grafana.emergency-app.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: grafana-service
            port:
              number: 3000
---
# TLS Ingress (for HTTPS)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: emergency-app-ingress-tls
  namespace: emergency-response
  labels:
    app: emergency-response-app
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - emergency-app.local
    - monitoring.emergency-app.local
    - grafana.emergency-app.local
    secretName: emergency-app-tls
  rules:
  - host: emergency-app.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: nginx-service
            port:
              number: 80
  - host: monitoring.emergency-app.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: monitoring-service
            port:
              number: 9999
  - host: grafana.emergency-app.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: grafana-service
            port:
              number: 3000
