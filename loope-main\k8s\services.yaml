apiVersion: v1
kind: Service
metadata:
  name: emergency-app-service
  namespace: emergency-response
  labels:
    app: emergency-response-app
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: emergency-response-app
---
apiVersion: v1
kind: Service
metadata:
  name: monitoring-service
  namespace: emergency-response
  labels:
    app: monitoring-dashboard
spec:
  type: ClusterIP
  ports:
  - port: 9999
    targetPort: 9999
    protocol: TCP
    name: http
  selector:
    app: monitoring-dashboard
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
  namespace: emergency-response
  labels:
    app: emergency-nginx
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: emergency-nginx
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: emergency-response
  labels:
    app: redis
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: redis
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-service
  namespace: emergency-response
  labels:
    app: prometheus
spec:
  type: ClusterIP
  ports:
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: http
  selector:
    app: prometheus
---
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: emergency-response
  labels:
    app: grafana
spec:
  type: NodePort
  ports:
  - port: 3000
    targetPort: 3000
    nodePort: 30300
    protocol: TCP
    name: http
  selector:
    app: grafana
---
# Headless service for StatefulSet (if needed)
apiVersion: v1
kind: Service
metadata:
  name: emergency-app-headless
  namespace: emergency-response
  labels:
    app: emergency-response-app
spec:
  clusterIP: None
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: emergency-response-app
