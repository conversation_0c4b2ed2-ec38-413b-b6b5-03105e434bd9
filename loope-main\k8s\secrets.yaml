apiVersion: v1
kind: Secret
metadata:
  name: emergency-app-secrets
  namespace: emergency-response
  labels:
    app: emergency-response-app
type: Opaque
data:
  # Base64 encoded values
  # To encode: echo -n "your-secret" | base64
  SECRET_KEY: ZW1lcmdlbmN5LXJlc3BvbnNlLXNlY3JldC1rZXktMjAyNA== # emergency-response-secret-key-2024
  DATABASE_PASSWORD: ZW1lcmdlbmN5MTIz # emergency123
  REDIS_PASSWORD: cmVkaXMxMjM= # redis123
---
apiVersion: v1
kind: Secret
metadata:
  name: grafana-secrets
  namespace: emergency-response
  labels:
    app: grafana
type: Opaque
data:
  # Base64 encoded values
  GF_SECURITY_ADMIN_USER: YWRtaW4= # admin
  GF_SECURITY_ADMIN_PASSWORD: ZW1lcmdlbmN5MTIz # emergency123
---
apiVersion: v1
kind: Secret
metadata:
  name: registry-secret
  namespace: emergency-response
  labels:
    app: emergency-response-app
type: kubernetes.io/dockerconfigjson
data:
  # Docker registry credentials (if using private registry)
  # Create with: kube<PERSON>l create secret docker-registry registry-secret --docker-server=your-registry --docker-username=user --docker-password=pass --docker-email=email
  .dockerconfigjson: eyJhdXRocyI6e319
