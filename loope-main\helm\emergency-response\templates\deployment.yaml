apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "emergency-response.fullname" . }}-app
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "emergency-response.labels" . | nindent 4 }}
    component: app
spec:
  {{- if not .Values.app.autoscaling.enabled }}
  replicas: {{ .Values.app.replicaCount }}
  {{- end }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      {{- include "emergency-response.selectorLabels" . | nindent 6 }}
      component: app
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
        prometheus.io/scrape: "true"
        prometheus.io/port: "{{ .Values.app.service.port }}"
        prometheus.io/path: "/metrics"
      labels:
        {{- include "emergency-response.selectorLabels" . | nindent 8 }}
        component: app
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "emergency-response.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}-app
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.app.image.repository }}:{{ .Values.app.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.app.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.app.service.targetPort }}
              protocol: TCP
          env:
            - name: FLASK_ENV
              value: {{ .Values.app.env.FLASK_ENV | quote }}
            - name: DATABASE_PATH
              value: {{ .Values.app.env.DATABASE_PATH | quote }}
            - name: PORT
              value: {{ .Values.app.env.PORT | quote }}
            - name: LOG_LEVEL
              value: {{ .Values.app.env.LOG_LEVEL | quote }}
            - name: SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "emergency-response.fullname" . }}-secret
                  key: secret-key
            - name: REDIS_URL
              value: "redis://{{ include "emergency-response.fullname" . }}-redis-master:6379"
          livenessProbe:
            httpGet:
              path: /api/v1/health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /api/v1/health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          startupProbe:
            httpGet:
              path: /api/v1/health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 30
          resources:
            {{- toYaml .Values.app.resources | nindent 12 }}
          volumeMounts:
            - name: app-data
              mountPath: {{ .Values.app.persistence.dataPath }}
            - name: app-logs
              mountPath: {{ .Values.app.persistence.logsPath }}
      volumes:
        - name: app-data
          {{- if .Values.app.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "emergency-response.fullname" . }}-data-pvc
          {{- else }}
          emptyDir: {}
          {{- end }}
        - name: app-logs
          {{- if .Values.app.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ include "emergency-response.fullname" . }}-logs-pvc
          {{- else }}
          emptyDir: {}
          {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
---
{{- if .Values.monitoring.dashboard.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "emergency-response.fullname" . }}-monitoring
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "emergency-response.labels" . | nindent 4 }}
    component: monitoring
spec:
  {{- if not .Values.monitoring.dashboard.autoscaling.enabled }}
  replicas: {{ .Values.monitoring.dashboard.replicaCount }}
  {{- end }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      {{- include "emergency-response.selectorLabels" . | nindent 6 }}
      component: monitoring
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "{{ .Values.monitoring.dashboard.service.port }}"
        prometheus.io/path: "/metrics"
      labels:
        {{- include "emergency-response.selectorLabels" . | nindent 8 }}
        component: monitoring
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "emergency-response.serviceAccountName" . }}
      containers:
        - name: {{ .Chart.Name }}-monitoring
          image: "{{ .Values.monitoring.dashboard.image.repository }}:{{ .Values.monitoring.dashboard.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.monitoring.dashboard.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.monitoring.dashboard.service.targetPort }}
              protocol: TCP
          env:
            - name: PORT
              value: "{{ .Values.monitoring.dashboard.service.port }}"
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            {{- toYaml .Values.monitoring.dashboard.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
---
{{- if .Values.nginx.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "emergency-response.fullname" . }}-nginx
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "emergency-response.labels" . | nindent 4 }}
    component: nginx
spec:
  {{- if not .Values.nginx.autoscaling.enabled }}
  replicas: {{ .Values.nginx.replicaCount }}
  {{- end }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      {{- include "emergency-response.selectorLabels" . | nindent 6 }}
      component: nginx
  template:
    metadata:
      labels:
        {{- include "emergency-response.selectorLabels" . | nindent 8 }}
        component: nginx
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "emergency-response.serviceAccountName" . }}
      containers:
        - name: {{ .Chart.Name }}-nginx
          image: "{{ .Values.nginx.image.repository }}:{{ .Values.nginx.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.nginx.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.nginx.service.targetPort }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 30
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 3
          resources:
            {{- toYaml .Values.nginx.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
