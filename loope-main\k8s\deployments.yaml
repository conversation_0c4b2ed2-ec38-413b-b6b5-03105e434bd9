apiVersion: apps/v1
kind: Deployment
metadata:
  name: emergency-app-deployment
  namespace: emergency-response
  labels:
    app: emergency-response-app
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: emergency-response-app
  template:
    metadata:
      labels:
        app: emergency-response-app
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      imagePullSecrets:
        - name: registry-secret
      containers:
      - name: emergency-app
        image: emergency-response-app:v1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        env:
        - name: FLASK_ENV
          valueFrom:
            configMapKeyRef:
              name: emergency-app-config
              key: FLASK_ENV
        - name: DATABASE_PATH
          valueFrom:
            configMapKeyRef:
              name: emergency-app-config
              key: DATABASE_PATH
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: emergency-app-config
              key: PORT
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: emergency-app-secrets
              key: SECRET_KEY
        - name: REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: emergency-app-config
              key: REDIS_URL
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        volumeMounts:
        - name: app-data
          mountPath: /app/data
        - name: app-logs
          mountPath: /app/logs
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /api/v1/health
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      volumes:
      - name: app-data
        persistentVolumeClaim:
          claimName: emergency-app-data-pvc
      - name: app-logs
        persistentVolumeClaim:
          claimName: emergency-app-logs-pvc
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: monitoring-deployment
  namespace: emergency-response
  labels:
    app: monitoring-dashboard
    version: v1.0.0
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: monitoring-dashboard
  template:
    metadata:
      labels:
        app: monitoring-dashboard
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9999"
        prometheus.io/path: "/metrics"
    spec:
      imagePullSecrets:
        - name: registry-secret
      containers:
      - name: monitoring-dashboard
        image: monitoring-dashboard:v1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 9999
          name: http
          protocol: TCP
        env:
        - name: PORT
          value: "9999"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9999
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 9999
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  namespace: emergency-response
  labels:
    app: emergency-nginx
    version: v1.0.0
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: emergency-nginx
  template:
    metadata:
      labels:
        app: emergency-nginx
        version: v1.0.0
    spec:
      imagePullSecrets:
        - name: registry-secret
      containers:
      - name: nginx
        image: nginx-proxy:v1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 80
          name: http
          protocol: TCP
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-deployment
  namespace: emergency-response
  labels:
    app: redis
    version: v1.0.0
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        version: v1.0.0
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 6379
          name: redis
          protocol: TCP
        command:
        - redis-server
        - --appendonly
        - "yes"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
