<?xml version="1.0" encoding="utf-8"?><testsuites name="pytest tests"><testsuite name="pytest" errors="1" failures="0" skipped="0" tests="1" time="4.759" timestamp="2025-06-24T12:55:26.118675+01:00" hostname="DESKTOP-EVPP8P0"><testcase classname="" name="tests.test_integration" time="0.000"><error message="collection failure">ImportError while importing test module 'C:\Users\<USER>\Desktop\loopes\loope\tests\test_integration.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
C:\Program Files\Python312\Lib\importlib\__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests\test_integration.py:21: in &lt;module&gt;
    from auth import hash_password, generate_api_key
E   ImportError: cannot import name 'hash_password' from 'auth' (C:\Users\<USER>\Desktop\loopes\loope\auth.py)</error></testcase></testsuite></testsuites>