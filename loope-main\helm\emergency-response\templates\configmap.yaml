apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "emergency-response.configmapName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "emergency-response.labels" . | nindent 4 }}
data:
  FLASK_ENV: {{ .Values.app.env.FLASK_ENV | quote }}
  DATABASE_PATH: {{ .Values.app.env.DATABASE_PATH | quote }}
  PORT: {{ .Values.app.env.PORT | quote }}
  LOG_LEVEL: {{ .Values.app.env.LOG_LEVEL | quote }}
  REDIS_URL: "redis://{{ include "emergency-response.fullname" . }}-redis-master:6379"
  PROMETHEUS_METRICS: "true"
---
{{- if .Values.nginx.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "emergency-response.fullname" . }}-nginx-config
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "emergency-response.labels" . | nindent 4 }}
    component: nginx
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        access_log /var/log/nginx/access.log main;

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;

        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;

        upstream emergency_app {
            server {{ include "emergency-response.fullname" . }}-app:{{ .Values.app.service.port }};
            keepalive 32;
        }

        {{- if .Values.monitoring.dashboard.enabled }}
        upstream monitoring_dashboard {
            server {{ include "emergency-response.fullname" . }}-monitoring:{{ .Values.monitoring.dashboard.service.port }};
            keepalive 16;
        }
        {{- end }}

        server {
            listen 80;
            server_name _;

            location / {
                proxy_pass http://emergency_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
                
                proxy_buffering on;
                proxy_buffer_size 4k;
                proxy_buffers 8 4k;
            }

            location /api/ {
                proxy_pass http://emergency_app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                proxy_connect_timeout 10s;
                proxy_send_timeout 10s;
                proxy_read_timeout 10s;
            }

            {{- if .Values.monitoring.dashboard.enabled }}
            location /monitoring/ {
                proxy_pass http://monitoring_dashboard/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            {{- end }}

            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            location /metrics {
                proxy_pass http://emergency_app/metrics;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            }
        }
    }
{{- end }}
