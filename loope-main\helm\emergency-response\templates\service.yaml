apiVersion: v1
kind: Service
metadata:
  name: {{ include "emergency-response.fullname" . }}-app
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "emergency-response.labels" . | nindent 4 }}
    component: app
spec:
  type: {{ .Values.app.service.type }}
  ports:
    - port: {{ .Values.app.service.port }}
      targetPort: {{ .Values.app.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "emergency-response.selectorLabels" . | nindent 4 }}
    component: app
---
{{- if .Values.monitoring.dashboard.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "emergency-response.fullname" . }}-monitoring
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "emergency-response.labels" . | nindent 4 }}
    component: monitoring
spec:
  type: {{ .Values.monitoring.dashboard.service.type }}
  ports:
    - port: {{ .Values.monitoring.dashboard.service.port }}
      targetPort: {{ .Values.monitoring.dashboard.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "emergency-response.selectorLabels" . | nindent 4 }}
    component: monitoring
{{- end }}
---
{{- if .Values.nginx.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "emergency-response.fullname" . }}-nginx
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "emergency-response.labels" . | nindent 4 }}
    component: nginx
  {{- if eq .Values.nginx.service.type "LoadBalancer" }}
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
  {{- end }}
spec:
  type: {{ .Values.nginx.service.type }}
  ports:
    - port: {{ .Values.nginx.service.port }}
      targetPort: {{ .Values.nginx.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "emergency-response.selectorLabels" . | nindent 4 }}
    component: nginx
{{- end }}
---
# Headless service for StatefulSet discovery
apiVersion: v1
kind: Service
metadata:
  name: {{ include "emergency-response.fullname" . }}-headless
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "emergency-response.labels" . | nindent 4 }}
    component: app
spec:
  clusterIP: None
  ports:
    - port: {{ .Values.app.service.port }}
      targetPort: {{ .Values.app.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "emergency-response.selectorLabels" . | nindent 4 }}
    component: app
