{% extends "base.html" %}

{% block title %}Medical Emergency AI Assistant{% endblock %}

{% block extra_head %}
<style>
.chat-container {
    height: 70vh;
    border: 1px solid #ddd;
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chat-header {
    background: linear-gradient(135deg, #dc3545, #fd7e14);
    color: white;
    padding: 15px;
    text-align: center;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.message.user {
    justify-content: flex-end;
}

.message.bot {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
}

.message.user .message-content {
    background-color: #007bff;
    color: white;
    border-bottom-right-radius: 4px;
}

.message.bot .message-content {
    background-color: white;
    color: #333;
    border: 1px solid #ddd;
    border-bottom-left-radius: 4px;
}

.message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 10px;
    font-size: 16px;
}

.user-avatar {
    background-color: #007bff;
    color: white;
}

.bot-avatar {
    background-color: #dc3545;
    color: white;
}

.chat-input {
    padding: 15px;
    border-top: 1px solid #ddd;
    background-color: white;
}

.typing-indicator {
    display: none;
    padding: 10px;
    font-style: italic;
    color: #666;
}

.emergency-shortcuts {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.emergency-shortcut {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 20px;
    padding: 5px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.emergency-shortcut:hover {
    background-color: #ffc107;
    color: white;
}

.emergency-shortcut.critical {
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.emergency-shortcut.critical:hover {
    background-color: #dc3545;
    color: white;
}

.disclaimer {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 20px;
    font-size: 14px;
}

.emergency-call {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 25px;
    padding: 10px 20px;
    font-weight: bold;
    margin-bottom: 15px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.emergency-call:hover {
    background-color: #c82333;
    color: white;
    text-decoration: none;
    transform: scale(1.05);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <!-- Emergency Call Button -->
            <div class="text-center mb-3">
                <a href="tel:119" class="emergency-call">
                    <i class="fas fa-phone me-2"></i>EMERGENCY CALL 119
                </a>
            </div>

            <!-- Disclaimer -->
            <div class="disclaimer">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Disclaimer:</strong> This AI assistant provides general first aid guidance only. 
                For life-threatening emergencies, call 119 immediately. This is not a substitute for professional medical advice.
            </div>

            <!-- Chat Container -->
            <div class="chat-container">
                <div class="chat-header">
                    <h4><i class="fas fa-robot me-2"></i>Medical Emergency AI Assistant</h4>
                    <small>Ask me about first aid, emergency procedures, and medical emergencies</small>
                </div>

                <div class="chat-messages" id="chatMessages">
                    <!-- Welcome message -->
                    <div class="message bot">
                        <div class="message-avatar bot-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <strong>🤖 Medical Emergency AI Assistant</strong><br><br>
                            Hello! I'm here to help with medical emergencies and first aid guidance.<br><br>
                            <strong>I can help with:</strong><br>
                            • CPR instructions<br>
                            • Choking relief<br>
                            • Bleeding control<br>
                            • Burn treatment<br>
                            • Fracture management<br>
                            • And much more!<br><br>
                            <strong>⚠️ Remember:</strong> For life-threatening emergencies, call 119 immediately!<br><br>
                            What medical emergency can I help you with?
                        </div>
                    </div>
                </div>

                <div class="typing-indicator" id="typingIndicator">
                    <div class="message bot">
                        <div class="message-avatar bot-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <i class="fas fa-spinner fa-spin me-2"></i>AI is thinking...
                        </div>
                    </div>
                </div>

                <div class="chat-input">
                    <!-- Emergency Shortcuts -->
                    <div class="emergency-shortcuts">
                        <span class="emergency-shortcut critical" onclick="sendQuickMessage('heart attack')">❤️ Heart Attack</span>
                        <span class="emergency-shortcut critical" onclick="sendQuickMessage('choking')">🫁 Choking</span>
                        <span class="emergency-shortcut critical" onclick="sendQuickMessage('bleeding')">🩸 Bleeding</span>
                        <span class="emergency-shortcut" onclick="sendQuickMessage('burn')">🔥 Burns</span>
                        <span class="emergency-shortcut" onclick="sendQuickMessage('fracture')">🦴 Fracture</span>
                        <span class="emergency-shortcut" onclick="sendQuickMessage('cpr')">❤️ CPR</span>
                        <span class="emergency-shortcut" onclick="sendQuickMessage('seizure')">🧠 Seizure</span>
                        <span class="emergency-shortcut critical" onclick="sendQuickMessage('poisoning')">☠️ Poisoning</span>
                    </div>

                    <!-- Message Input -->
                    <div class="input-group">
                        <input type="text" class="form-control" id="messageInput" 
                               placeholder="Ask about medical emergencies... (e.g., 'How to treat burns?')" 
                               onkeypress="handleKeyPress(event)">
                        <button class="btn btn-primary" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Additional Resources -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <i class="fas fa-book-medical me-2"></i>First Aid Practices
                        </div>
                        <div class="card-body">
                            <p>Access detailed first aid guides with videos and step-by-step instructions.</p>
                            <a href="{{ url_for('first_aid') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-external-link-alt me-1"></i>View Guides
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <i class="fas fa-map-marked-alt me-2"></i>Emergency Map
                        </div>
                        <div class="card-body">
                            <p>Find nearest hospitals and fire stations using smart pathfinding.</p>
                            <a href="{{ url_for('map_page') }}" class="btn btn-warning btn-sm">
                                <i class="fas fa-external-link-alt me-1"></i>Open Map
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function sendMessage() {
    const input = document.getElementById('messageInput');
    const message = input.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addMessage(message, 'user');
    input.value = '';
    
    // Show typing indicator
    showTypingIndicator();
    
    // Send to AI
    fetch('/medical-chatbot', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: message })
    })
    .then(response => response.json())
    .then(data => {
        hideTypingIndicator();
        addMessage(data.response, 'bot');
    })
    .catch(error => {
        hideTypingIndicator();
        addMessage('Sorry, I encountered an error. Please try again or call 119 for emergencies.', 'bot');
    });
}

function sendQuickMessage(message) {
    document.getElementById('messageInput').value = message;
    sendMessage();
}

function addMessage(content, sender) {
    const messagesContainer = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;
    
    const avatar = sender === 'user' ? 
        '<div class="message-avatar user-avatar"><i class="fas fa-user"></i></div>' :
        '<div class="message-avatar bot-avatar"><i class="fas fa-robot"></i></div>';
    
    const messageContent = content.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    
    if (sender === 'user') {
        messageDiv.innerHTML = `
            <div class="message-content">${messageContent}</div>
            ${avatar}
        `;
    } else {
        messageDiv.innerHTML = `
            ${avatar}
            <div class="message-content">${messageContent}</div>
        `;
    }
    
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function showTypingIndicator() {
    document.getElementById('typingIndicator').style.display = 'block';
    const messagesContainer = document.getElementById('chatMessages');
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function hideTypingIndicator() {
    document.getElementById('typingIndicator').style.display = 'none';
}

function handleKeyPress(event) {
    if (event.key === 'Enter') {
        sendMessage();
    }
}

// Auto-focus on input
document.getElementById('messageInput').focus();
</script>
{% endblock %}
