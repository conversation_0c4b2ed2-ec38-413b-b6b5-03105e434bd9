{% extends "base.html" %}

{% block title %}Login - Emergency Response App{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h3><i class="fas fa-sign-in-alt me-2"></i>Login</h3>
                </div>
                <div class="card-body">
                    {% if error %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
                        </div>
                    {% endif %}
                    
                    {% if success %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>{{ success }}
                        </div>
                    {% endif %}
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username or Email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" class="form-control" id="username" name="username"
                                       placeholder="Enter your username or email" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                            <label class="form-check-label" for="remember_me">
                                Remember me
                            </label>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Login
                            </button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p>Don't have an account? <a href="{{ url_for('register') }}">Register here</a></p>
                    </div>
                    
                    <hr>
                    
                    <div class="text-center">
                        <h6 class="text-muted">Emergency Hotlines</h6>
                        <div class="row">
                            <div class="col-4">
                                <small class="text-danger"><strong>Fire: 118</strong></small>
                            </div>
                            <div class="col-4">
                                <small class="text-primary"><strong>Police: 117</strong></small>
                            </div>
                            <div class="col-4">
                                <small class="text-success"><strong>Medical: 119</strong></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus on username field
    document.getElementById('username').focus();
    
    // Add form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        
        if (!username || !password) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return;
        }
    });
});
</script>
{% endblock %}
