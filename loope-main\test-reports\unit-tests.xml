<?xml version="1.0" encoding="utf-8"?><testsuites name="pytest tests"><testsuite name="pytest" errors="2" failures="0" skipped="0" tests="2" time="1.089" timestamp="2025-06-24T12:13:49.697636+01:00" hostname="DESKTOP-EVPP8P0"><testcase classname="" name="tests.test_database" time="0.000"><error message="collection failure">ImportError while importing test module 'C:\Users\<USER>\Desktop\loopes\loope\tests\test_database.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
C:\Program Files\Python312\Lib\importlib\__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests\test_database.py:16: in &lt;module&gt;
    from database import (
E   ImportError: cannot import name 'verify_user' from 'database' (C:\Users\<USER>\Desktop\loopes\loope\database.py)</error></testcase><testcase classname="" name="tests.test_auth" time="0.000"><error message="collection failure">ImportError while importing test module 'C:\Users\<USER>\Desktop\loopes\loope\tests\test_auth.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
C:\Program Files\Python312\Lib\importlib\__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
tests\test_auth.py:16: in &lt;module&gt;
    from auth import (
E   ImportError: cannot import name 'hash_password' from 'auth' (C:\Users\<USER>\Desktop\loopes\loope\auth.py)</error></testcase></testsuite></testsuites>